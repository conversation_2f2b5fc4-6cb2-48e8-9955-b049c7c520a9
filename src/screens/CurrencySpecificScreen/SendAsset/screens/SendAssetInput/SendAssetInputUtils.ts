import {AuthAddresses, AuthAsset, AuthUserWallet} from '@/types/authTypes';
import {Address, getAddressEncoder, getProgramDerivedAddress} from '@solana/kit';
import {PublicKey as SolanaWeb3PublicKey} from '@solana/web3.js';
import {NativeModules} from 'react-native';
import {coinToData} from '../../../../../constants/AbiConstants';
import {
  ChainToTokens,
  EVMChains,
  EVMChainsIds,
  SymbolToChain,
  UTXOChains,
} from '../../../../../constants/Chains';
import {WalletLoggerInstance} from '../../../../../services/BackendServices';
import WalletService from '../../../../../services/WalletService';
import {TrailingZeroRegex} from '../../../../../utils/parsing';
import {findWalletIndex} from '../../../currencySpecificUtils';
import {getSolanaMintAddress} from '../../sendAssetUtils';

const {WalletManagerBridge} = NativeModules;

export const validateAddressForAsset = async (address: string, blockchain: string) => {
  const {data} = await WalletLoggerInstance.post(
    `/v1/tools/validate-address/${blockchain}`,
    {
      address,
    },
  );

  return data.isValid;
};

export const calculateApproximateValue = (
  amount: string,
  tokenPrices: string[] | string[][],
  walletIndex: number,
  stableCoinIndex: number,
) => {
  let approximateValue;

  if (tokenPrices instanceof Array && tokenPrices[0] instanceof Array) {
    approximateValue =
      parseFloat(tokenPrices[walletIndex][stableCoinIndex]) * parseFloat(amount) == 0
        ? '0'
        : (
            parseFloat(tokenPrices[walletIndex][stableCoinIndex]) * parseFloat(amount)
          ).toFixed(2);
  } else {
    approximateValue = // @ts-ignore
      parseFloat(tokenPrices[stableCoinIndex]) * parseFloat(amount) == 0
        ? '0'
        : // @ts-ignore
          (parseFloat(tokenPrices[stableCoinIndex]) * parseFloat(amount)).toFixed(2);
  }
  let integerPartLength = parseFloat(approximateValue).toFixed(0).length;
  let decimalPlaces = Math.max(0, 5 - integerPartLength);
  approximateValue = parseFloat(approximateValue).toFixed(decimalPlaces);
  console.log(approximateValue);
  if (approximateValue == 'NaN') {
    return '0';
  }
  return Number(approximateValue).toFixed(2) == '0.00'
    ? '0'
    : Number(approximateValue).toFixed(2);
};

const calculateOutputs = (tx: any, data: any, value: string) => {
  try {
    let firstInputAddress = tx.data.inputs[0].address;
    let firstInputScript = tx.data.inputs[0].script;

    let index = 0;

    tx.data.outputs.forEach((output: any, i: number) => {
      if (output.satoshis === BigInt(Math.round(parseFloat(value) * 10 ** 8))) {
        index = i;
      }

      if (
        data.potentialxpubaddresses != undefined &&
        data.potentialxpubaddresses.includes(output.address)
      ) {
        tx.data.outputs[i].address = firstInputAddress;
        tx.data.outputs[i].script = firstInputScript;
      }
      if (
        data.xpubaddresses != undefined &&
        data.xpubaddresses.includes({address: output.address})
      ) {
        tx.data.outputs[i].address = firstInputAddress;
        tx.data.outputs[i].script = firstInputScript;
      }
    });
    let temp = tx.data.outputs[0];
    tx.data.outputs[0] = tx.data.outputs[index];
    tx.data.outputs[index] = temp;

    return tx;
  } catch (err) {
    console.error(err);
  }
};

export const getPreparedTx = async (
  stableCoin: string,
  asset: AuthAsset,
  wallets: AuthUserWallet[],
  receiverAddress: string,
  amount: number,
  userAddresses: AuthAddresses,
) => {
  const assetChain = SymbolToChain[asset.tokenSymbol.toLowerCase()];
  const walletIndex = findWalletIndex(asset.tokenSymbol);

  if (UTXOChains.includes(assetChain)) {
    const amountInSats = parseFloat(amount.toString()) * 10 ** 8;

    // 1. Get prepared transaction data
    const backendResponse = await new WalletService().prepareUTXOBasedTransaction(
      wallets[walletIndex],
      receiverAddress,
      amountInSats,
    );

    console.log('Backend UTXO response:', backendResponse);

    // 2. Get private key from user addresses
    const userAddress = userAddresses[walletIndex];
    if (!userAddress) {
      throw new Error('User address not found for wallet index');
    }

    const privateKey =
      'privateKey' in userAddress
        ? userAddress.privateKey
        : userAddress.addresses[0]?.privateKey;

    if (!privateKey) {
      throw new Error('Private key not found for UTXO transaction');
    }

    // 3. Sign the transaction
    const signedTxHex = await new WalletService().prepareAndSignUtxoTransaction(
      backendResponse,
      privateKey,
      wallets[walletIndex].blockchain,
    );

    console.log('Signed UTXO transaction hex:', signedTxHex);

    // 4. Return the signed transaction hex and fee information
    return {
      tx: {
        signedTxHex: signedTxHex,
        fee: backendResponse.estimatedFee,
        feeRate: backendResponse.feeRate,
        inputs: backendResponse.inputs,
        outputs: backendResponse.outputs,
      },
      wallet: wallets[walletIndex],
      privKey: privateKey,
      isUtxoSigned: true,
    };
  } else if (EVMChains.includes(assetChain)) {
    if (stableCoin != '') {
      const tx = await new WalletService().prepareStableCoinTransaction(
        wallets[walletIndex],
        coinToData[asset?.tokenSymbol][stableCoin],
        amount,
        EVMChainsIds[wallets[walletIndex].blockchain], // @ts-ignore
        userAddresses[walletIndex].address,
        receiverAddress,
      );

      return {
        tx: tx,
        wallet: wallets[walletIndex], // @ts-ignore
        privKey: userAddresses[walletIndex].privateKey,
      };
    }

    const tx = await new WalletService().prepareEvmBasedTransaction(
      wallets[walletIndex], // @ts-ignore
      userAddresses[walletIndex].address,
      receiverAddress,
      amount,
    );

    return {
      tx: tx,
      wallet: wallets[walletIndex], // @ts-ignore
      privKey: userAddresses[walletIndex].privateKey,
    };
  } else {
    const {data} = await new WalletService().prepareKaspaTransaction(
      // @ts-ignore
      userAddresses[walletIndex].addresses,
      receiverAddress,
      amount,
    );

    let tx = JSON.parse(data.transaction);
    tx.gasPrice = data.fee * 10 ** 18;

    return {
      tx: tx,
      wallet: wallets[walletIndex], // @ts-ignore
      privKey: userAddresses[walletIndex].privateKey,
    };
  }
};

export const parseCoinsToOptions = (
  coins: string[],
  assets: AuthAssets,
  stableCoins: string[][],
) => {
  console.log(assets, stableCoins);
  return coins.map((coin) => {
    try {
      let name = '';
      let coinAmount = '';

      if (coin.includes('-')) {
        name = `${coin.split('-')[1]}(${coin.split('-')[0]})`;

        let walletIndex = findWalletIndex(coin.split('-')[0]);
        ChainToTokens[assets[walletIndex].blockchain].forEach((token, index) => {
          if (token.tokenSymbol === coin.split('-')[1]) {
            coinAmount = stableCoins![walletIndex][index];
            let coinAmountBeforeDecimal = coinAmount.split('.')[0];
            let coinAmountAfterDecimal = coinAmount
              .split('.')[1]
              ?.replace(TrailingZeroRegex, '');
            coinAmount = coinAmountAfterDecimal
              ? coinAmountBeforeDecimal + '.' + coinAmountAfterDecimal
              : coinAmountBeforeDecimal;
          }
        });
      } else {
        name = coin;
        const asset = assets.find((asset) => asset.tokenSymbol === coin);
        coinAmount = asset?.amount!;
        let coinAmountBeforeDecimal = coinAmount.split('.')[0];
        let coinAmountAfterDecimal = coinAmount
          .split('.')[1]
          ?.replace(TrailingZeroRegex, '');
        coinAmount = coinAmountAfterDecimal
          ? coinAmountBeforeDecimal + '.' + coinAmountAfterDecimal
          : coinAmountBeforeDecimal;
      }

      return {
        value: coin,
        label: `${name} - ${coinAmount}`,
      };
    } catch (error) {
      console.log(error);
      return {
        value: coin,
        label: coin,
      };
    }
  });
};

export const prepareSolanaTransaction = async (
  recipientAddress: string,
  amount: number,
  userAddresses: AuthAddresses,
) => {
  const walletIndex = findWalletIndex('SOL');

  console.log('walletIndex', walletIndex);
  console.log('userAddresses[walletIndex].address', userAddresses[walletIndex].address);
  console.log('recipientAddress', recipientAddress);
  console.log('amount', amount);

  const tx = await WalletManagerBridge.buildUnsignedSolanaTransaction(
    userAddresses[walletIndex].address,
    recipientAddress,
    Number(amount),
  );

  try {
    const response = await fetch(
      'https://dev.assetify.net/balance-fetcher/transaction/solana/mainnet/v2/getfee',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: tx,
      },
    );

    if (!response.ok) {
      throw new Error('Failed to fetch fee information');
    }

    const feeData = await response.json();

    const baseFeeLamports = Number(feeData.solana.fees.baseFee);
    const calculatedFee = (baseFeeLamports / 1_000_000_000).toFixed(6);

    return {
      tx: tx,
      feeData: feeData,
      calculatedFee: calculatedFee,
    } as any;
  } catch (error) {
    console.log(error);
  }
};

export const sendAndBroadcastSolanaTransaction = async (
  address,
  builtTx,
  stableCoin,
  asset,
  userAddresses,
  recipientAddress,
  amount,
  solanaPDAWallet,
) => {
  const ownerAddress = address?.address as Address;

  const tokenProgramAddress = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' as Address;
  const ataProgramId = 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL' as Address;

  console.log('asset', asset);

  const response = await fetch(
    'https://dev.assetify.net/balance-fetcher/transaction/solana/mainnet/v2/getfee',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: builtTx,
    },
  );

  console.log('response', response);

  if (!response.ok) {
    throw new Error('Failed to fetch fee information');
  }

  const feeData = await response.json();
  const freshSolanaBlockhash = feeData.solana.blockhash;

  console.log('freshSolanaBlockhash', freshSolanaBlockhash);

  console.log('stableCoin', stableCoin);
  // Solana Token logic
  if (stableCoin) {
    const tokenMintAddress = getSolanaMintAddress(stableCoin);

    const addressEncoder = getAddressEncoder();

    // Derive sender's associated token account (ATA)
    const pdaSender = await getProgramDerivedAddress({
      programAddress: ataProgramId,
      seeds: [
        addressEncoder.encode(ownerAddress as Address), // sender wallet address
        addressEncoder.encode(tokenProgramAddress), // token mint address
        addressEncoder.encode(tokenMintAddress as Address),
      ],
    });
    console.log('pdaSender', pdaSender);

    const isOnCurve = SolanaWeb3PublicKey.isOnCurve(recipientAddress);

    const pdaRecipient = await getProgramDerivedAddress({
      programAddress: ataProgramId,
      seeds: [
        addressEncoder.encode(
          // Wallet Address
          recipientAddress as Address,
        ),
        addressEncoder.encode(
          // Mint Address
          tokenProgramAddress,
        ),
        // Token Program Address
        addressEncoder.encode(tokenMintAddress as Address),
      ],
    });

    console.log('pdaRecipient', pdaRecipient);
    console.log('pdaSender', pdaSender);
    console.log('amount', amount);

    console.log('userAddresses[2].privateKey', userAddresses[2].privateKey);

    const senderTokenAddress = pdaSender[0]; // e.g., "Cyqexb2zv2kA2Wo3ws6Ne5B6wUyQE2oWmhp7fvcK2iDe"
    const recipientTokenAddress = pdaRecipient[0]; // e.g., "Feoe1mim72UXjs7dCNQuDGhDEhUX7kkckQxNzRYLunkR"

    console.log('sender and recipient usdc addresses');
    console.log(senderTokenAddress);
    console.log(recipientAddress);

    // Other parameters remain the same
    const fromAddress =
      typeof address === 'string' ? (address as Address) : (address?.address as Address); // wallet address (sender)
    const toAddress = recipientAddress; // recipient wallet address
    const value = parseFloat(amount) * 1e6; // use your amount value
    const tokenMint = tokenMintAddress;
    const decimals = 6;
    const blockhash = freshSolanaBlockhash;

    const privateKeyHex = userAddresses[2].privateKey?.toString();

    console.log('includes', solanaPDAWallet.includes(senderTokenAddress));

    const tx = await WalletManagerBridge.signSolanaTokenTransaction(
      fromAddress,
      toAddress,
      senderTokenAddress,
      isOnCurve ? recipientTokenAddress : recipientAddress,
      value,
      tokenMint,
      decimals,
      blockhash,
      privateKeyHex,
      isOnCurve ? !solanaPDAWallet.includes(recipientTokenAddress) : false,
    );

    console.log('tx', tx);

    const response = await fetch(
      'https://dev.assetify.net/balance-fetcher/broadcast-transaction/solana',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({tx: tx}),
      },
    );

    if (!response.ok) {
      console.log('error', response);
      return;
    }

    return true;
  }

  // Solana Coin logic
  if (!stableCoin) {
    console.log('SOL >>>>>');
    console.log('address', typeof address === 'string' ? address : address?.address);
    console.log('recipientAddress', recipientAddress);
    console.log('amount', amount);
    console.log('amount in lamports', parseFloat(amount) * 1e9);
    console.log('userAddresses[0].privateKey', userAddresses[2].privateKey);

    const addressString = typeof address === 'string' ? address : address?.address;
    const amountInLamports = parseFloat(amount) * 1e9;

    const tx = await WalletManagerBridge.signSolanaTransaction(
      addressString,
      recipientAddress,
      amountInLamports,
      freshSolanaBlockhash,
      userAddresses[2].privateKey,
    );

    console.log('tx', JSON.stringify({tx: tx}));

    await new Promise<void>((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000);
    });

    const response = await fetch(
      'https://dev.assetify.net/balance-fetcher/broadcast-transaction/solana',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({tx: tx}),
      },
    );

    if (!response.ok) {
      console.log('error', response);
      return;
    }

    console.log('response', response);

    return;
  }
};
