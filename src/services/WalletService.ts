import {Contract, TransactionRequest, Wallet, ethers, parseUnits} from 'ethers';
import {NativeModules} from 'react-native';
import {Wallet as xrplWallet} from 'xrpl';
const {Enumerations, Services} = require('@AssetifyNet/cryptoapis-kms');
const TronWeb = require('tronweb');

import {ChainToSymbol, ChainsOrder, EVMChainsIds} from '../constants/Chains';
import {LoggerRequest} from '../types/walletTypes';
import {WalletBalanceInstance, WalletLoggerInstance} from './BackendServices';
const {WalletManagerBridge} = NativeModules;

import {
  AuthAddress,
  AuthAddressType,
  AuthAddresses,
  AuthKaspaAddress,
  AuthUserWallet,
  NativeUserWallet,
} from '@/types/authTypes';

export default class WalletService {
  constructor() {}

  createWallets = async (mnemonic: string) => {
    const walletTypes = [
      Enumerations.Blockchains.BITCOIN,
      Enumerations.Blockchains.ETHEREUM,
      'solana',
      Enumerations.Blockchains.BINANCE_SMART_CHAIN,
      Enumerations.Blockchains.TRX,
      Enumerations.Blockchains.XRP,
      Enumerations.Blockchains.KASPA,
      Enumerations.Blockchains.AVALANCHE,
    ];

    let createdWallets: NativeUserWallet[] = [];

    try {
      const walletPromises: Promise<NativeUserWallet>[] = walletTypes.map(
        async (blockchain: string) => {
          if (blockchain !== Enumerations.Blockchains.KASPA) {
            return await WalletManagerBridge.createHDWalletFromMnemonic(
              mnemonic,
              blockchain,
            );
          } else {
            const walletService = new Services.WalletService(blockchain, 'mainnet');
            const wallet = await walletService.createHDWalletFromMnemonic(mnemonic);
            const formattedWallet: NativeUserWallet = {
              blockchain: blockchain,
              network: 'mainnet',
              mnemonic: wallet.mnemonic,
              seed: wallet.seed,
              xPubsList: wallet.xPubsList,
            };

            return formattedWallet;
          }
        },
      );

      createdWallets = await Promise.all(walletPromises);
      return createdWallets;
    } catch (error) {
      console.error(error);
    }
  };

  createAddresses = async (wallets: NativeUserWallet[]) => {
    console.log('wallets: ', wallets);
    const kaspaWallet = wallets.find(
      (wallet: NativeUserWallet) => wallet.blockchain === Enumerations.Blockchains.KASPA,
    );

    let kaspaAddresses: AuthAddress[] = [];
    try {
      const addressService = new Services.AddressService(
        kaspaWallet?.blockchain,
        'mainnet',
      );

      const walletService = new Services.WalletService(
        kaspaWallet?.blockchain,
        'mainnet',
      );

      const kaspaHDWallet = await walletService.createHDWalletFromMnemonic(
        kaspaWallet?.mnemonic,
      );

      for (let i = 0; i < 10; i++) {
        const address = await addressService.generateAddressFromHDWalletWithCustomPath(
          kaspaHDWallet,
          `/0/${i}`,
        );

        kaspaAddresses.push(address._data === undefined ? address : address._data);
      }
    } catch (error) {
      console.error(error);
    }

    const addresses = wallets.map((wallet: NativeUserWallet) => {
      if (wallet.blockchain !== Enumerations.Blockchains.KASPA) {
        return {
          address: wallet.address,
          privateKey: wallet.privateKey,
          publicKey: wallet.publicKey,
          chain: wallet.blockchain,
        };
      } else {
        return {
          addresses: kaspaAddresses,
          chain: Enumerations.Blockchains.KASPA,
        };
      }
    });

    return addresses;
  };

  walletLoggerBTC = async (btcAddress: string, xPubKey: string) =>
    WalletLoggerInstance.post(`/v1/wallet`, {
      bitcoin_address: btcAddress,
      bitcoin_xpub: xPubKey,
      bitcoin_network: 'mainnet',
    })
      .then((response) => response)
      .catch((error) => {
        if (error.response?.status === 409) {
          console.log(error.response);
          return error.response;
        } else {
          console.log('logger', error);
          throw error;
        }
      });

  createLoggerRequest = async (
    createdWallets: NativeUserWallet[],
    createdAddresses: AuthAddresses,
  ) => {
    let wallets = [];
    let addresses: AuthAddresses = [];
    let loggerRequest = [];

    console.log('createdAddresses: ', createdAddresses);

    let btcAddress = createdAddresses.find(
      (address: AuthAddressType) => address?.chain === Enumerations.Blockchains.BITCOIN,
    ) as AuthAddress;

    for (let i = 0; i < createdWallets.length; i++) {
      let index = ChainsOrder.indexOf(createdWallets[i].blockchain);
      wallets[index] = {
        seed: createdWallets[i].seed,
        zPub: createdWallets[i].xPubsList[0],
        mnemonic: createdWallets[i].mnemonic,
        blockchain: createdWallets[i].blockchain,
        network: createdWallets[i].network,
      };

      if (createdWallets[i].blockchain === Enumerations.Blockchains.BITCOIN) {
        addresses[index] = btcAddress;
      } else {
        let address = createdAddresses.find(
          (address: AuthAddressType) => address.chain === createdWallets[i].blockchain,
        ) as AuthAddress | AuthKaspaAddress;

        if (address === undefined) {
          console.error('Address not found', createdWallets[i].blockchain);
          throw new Error('Address not found');
        }

        if (address.chain === Enumerations.Blockchains.KASPA) {
          addresses[index] = {
            // @ts-ignore
            addresses: address.addresses,
            chain: address.chain,
          } as AuthAddressType;
        } else {
          addresses[index] = {
            ...address, // @ts-ignore
            address: address.address.replace('bitcoincash:', ''),
          };
        }

        loggerRequest[index - 1] = {
          btcAddress: btcAddress.address,
          chain: wallets[index].blockchain === 'trx' ? 'tron' : wallets[index].blockchain,
          xpub: wallets[index].zPub.accountXpub,
          address:
            addresses[index].chain === 'kaspa' // @ts-ignore
              ? addresses[index].addresses // @ts-ignore
              : addresses[index].address,
          network: wallets[index].network,
        };
      }
    }

    return {wallets, addresses, loggerRequest};
  };

  async walletLogger(loggerRequestArray: LoggerRequest[]) {
    return WalletLoggerInstance.post(
      `/v1/wallet/${loggerRequestArray[0]?.btcAddress}/add-xpub`,
      {
        chains: loggerRequestArray.map((loggerRequest: LoggerRequest) => {
          if (loggerRequest.address instanceof Array)
            return {
              chain: loggerRequest.chain,
              xpub: loggerRequest.xpub,
              address: loggerRequest.address[0].address,
              kaspaxPubAddresses: loggerRequest.address
                .map((a: any) => a.address)
                .join(','),
              network: loggerRequest.network,
            };
          return {
            chain: loggerRequest.chain,
            xpub: loggerRequest.xpub,
            address:
              loggerRequest.address instanceof Array
                ? loggerRequest.address[0].address
                : loggerRequest.address,
            network: loggerRequest.network,
          };
        }),
      },
    )
      .then((response) => response.data)
      .then((json) => {
        console.log('json', json);
      })
      .catch((error) => {
        console.error('Error in walletLogger:', error);
      });
  }

  async prepareUTXOBasedTransaction(
    wallet: AuthUserWallet,
    receiverAddress: string,
    amount: number,
  ): Promise<any> {
    await WalletLoggerInstance.post(
      `/v1/wallet/sync/${wallet.zPub.accountXpub}/${wallet.blockchain}/${wallet.network}`,
    );

    const {data} = await WalletBalanceInstance.post(
      `prepare-utxo-transaction/${wallet.blockchain}`,
      {
        payload: {
          data: {
            item: {
              xpub: wallet.zPub.accountXpub,
              fee: {
                priority: 'standard',
              },
              prepareStrategy: 'minimize-dust',
              recipients: [
                {
                  address: receiverAddress,
                  amount: amount.toString(),
                },
              ],
            },
          },
        },
      },
    );

    console.log('DATA >>>>', data);

    return {
      data: {
        inputs: data.data.item.blockchainSpecific.vin.map((input: any) => {
          return {
            address: input.address,
            txid: input.transactionId,
            satoshis: input.satoshis,
            script: input.script,
            outputIndex: input.outputIndex,
            derivationIndex: input.derivationIndex,
            change: input.change,
          };
        }),
        outputs: data.data.item.blockchainSpecific.vout,
        locktime: data.data.item?.locktime ? data.data.item.locktime : null,
        replaceable: data.data.item?.blockchainSpecific?.replaceable
          ? data.data.item.blockchainSpecific.replaceable
          : false,
        data: data.data.item?.data
          ? data.data.item.data
          : {
              inputs: data.data.item.blockchainSpecific.vin.map((input: any) => {
                return {
                  address: input.address,
                  txid: input.transactionId,
                  satoshis: input.satoshis,
                  script: input.script,
                  outputIndex: input.outputIndex,
                  derivationIndex: input.derivationIndex,
                  change: input.change,
                };
              }),
            },
        feePerByte: data.data.item?.feePerByte
          ? Number(Number(data.data.item.feePerByte).toFixed(8))
          : null,
        fee: data.data.item?.fee ? Number(data.data.item.fee) : 0,
        version: data.data.item?.version ? data.data.item.version : null,
      },
    };
  }

  async prepareStableCoinTransaction(
    authWallet: AuthUserWallet,
    coinData: {address: string; abi: any; decimals: number},
    value: number,
    chainId: number,
    senderAddress: string,
    recipientAddress: string,
  ): Promise<any> {
    const wallet = ethers.Wallet.fromPhrase(authWallet.mnemonic);
    const contract = new Contract(coinData.address, coinData.abi, wallet);
    const amount = parseUnits(value.toFixed(6), coinData.decimals);

    if (chainId === 1 || chainId === 43114) {
      const payload: TransactionRequest = {
        to: coinData.address,
        from: senderAddress,
        data: contract.interface.encodeFunctionData('transfer', [
          recipientAddress,
          amount,
        ]),
      };

      const feeInHex = await WalletBalanceInstance.post(
        `/transaction/${
          authWallet.blockchain === 'avalanche'
            ? authWallet.blockchain
            : ChainToSymbol[authWallet.blockchain]
        }/${authWallet.network}/getfeeinwei`,
        {
          tx_data: payload,
        },
      );

      payload.chainId = chainId;
      payload.gasPrice = parseInt(feeInHex.data.gasPrice, 16);
      payload.gasLimit = parseInt(feeInHex.data.estimatedGas, 16);
      payload.nonce = feeInHex.data.nonce;

      return payload;
    } else if (chainId === 56) {
      let tx = {
        sender: senderAddress,
        recipient: coinData.address,
        chainId: chainId,
        amount: 0,
        data: contract.interface.encodeFunctionData('transfer', [
          recipientAddress,
          amount,
        ]),
        gasPrice: 0,
        gasLimit: 0,
        nonce: 0,
      };

      const feeInHex = await WalletBalanceInstance.post(
        `/transaction/${
          authWallet.blockchain === 'avalanche'
            ? authWallet.blockchain
            : ChainToSymbol[authWallet.blockchain]
        }/${authWallet.network}/getfeeinwei`,
        {
          tx_data: {
            to: coinData.address,
            from: senderAddress,
            data: tx.data,
          },
        },
      );

      tx.gasPrice = parseInt(feeInHex.data.gasPrice, 16);
      tx.gasLimit = parseInt(feeInHex.data.estimatedGas, 16);
      tx.nonce = feeInHex.data.nonce;

      return tx;
    } else if (chainId === 195) {
      console.log({
        senderAddress: senderAddress,
        contractAddress: coinData.address,
        functionSelector: 'transfer(address,uint256)',
        amount: 0,
        callValue: 0,
        paramters: [
          {
            type: 'address',
            value: recipientAddress,
          },
          {
            type: 'uint256',
            value: amount.toString(),
          },
        ],
      });
      try {
        const {data} = await WalletBalanceInstance.post(`/generate-transaction/tron`, {
          senderAddress: senderAddress,
          contractAddress: coinData.address,
          functionSelector: 'transfer(address,uint256)',
          amount: 0,
          callValue: 0,
          parameters: [
            {
              type: 'address',
              value: recipientAddress,
            },
            {
              type: 'uint256',
              value: amount.toString(),
            },
          ],
        });

        const resp = await WalletBalanceInstance.get(`/check-tron-energy`);
        const energyPrice = resp.data.energyPrice;

        const energySun = data.energyEstimate * energyPrice;
        const estimateTrx = energySun / 10 ** 6;

        return {
          tx: data.transaction.transaction,
          bandwidth: data.bandwidthEstimate,
          estimateTrx: estimateTrx,
        };
      } catch (err) {
        console.error(err);
      }
    }
  }

  async prepareEvmBasedTransaction(
    authWallet: AuthUserWallet,
    senderAddress: string,
    recipientAddress: string,
    amount: number,
  ): Promise<any> {
    if (authWallet.blockchain === 'ethereum' || authWallet.blockchain === 'avalanche') {
      const valueInWei = Number(amount) * 10 ** 18;
      let payload: TransactionRequest = {
        to: recipientAddress,
        from: senderAddress,
        value: valueInWei.toString(),
        chainId: EVMChainsIds[authWallet.blockchain],
      };

      const feeInHex = await WalletBalanceInstance.post(
        `/transaction/${
          authWallet.blockchain === 'avalanche'
            ? authWallet.blockchain
            : ChainToSymbol[authWallet.blockchain]
        }/${authWallet.network}/getfeeinwei`,
        {
          tx_data: payload,
        },
      );

      payload.gasPrice = parseInt(feeInHex.data.gasPrice, 16);
      payload.gasLimit = parseInt(feeInHex.data.estimatedGas, 16);
      payload.nonce = feeInHex.data.nonce;

      return payload;
    } else if (authWallet.blockchain === 'xrp') {
      const {data} = await WalletBalanceInstance.post(
        `/generate-transaction/xrp/payment`,
        {
          transactionType: 'Payment',
          account: senderAddress,
          amount: (amount * 10 ** 6).toString(),
          destination: recipientAddress,
        },
      );

      return data.unsignedTx;
    } else if (authWallet.blockchain === 'trx') {
      const {data} = await WalletBalanceInstance.post(`/generate-transaction/tron`, {
        senderAddress: senderAddress,
        recipientAddress: recipientAddress,
        amount: (amount * 10 ** 6).toString(),
      });

      return {
        tx: data.transaction,
        bandwidthEstimate: data.bandwidthEstimate,
      };
    } else if (authWallet.blockchain === 'binance-smart-chain') {
      const valueInWei = amount * 10 ** 18;
      const valueInHex = '0x' + valueInWei.toString(16);

      let payload: TransactionRequest = {
        to: recipientAddress,
        from: senderAddress,
        value: valueInHex,
        chainId: EVMChainsIds[authWallet.blockchain],
      };

      try {
        const feeInHex = await WalletBalanceInstance.post(
          `/transaction/${ChainToSymbol[authWallet.blockchain]}/${
            authWallet.network
          }/getfeeinwei`,
          {
            tx_data: payload,
          },
        );

        payload.gasPrice = feeInHex.data.gasPrice;
        payload.gasLimit = feeInHex.data.estimatedGas;
        payload.nonce = feeInHex.data.nonce;

        // Required in this format: https://github.com/Crypto-APIs/Crypto_APIs_Key_Management_System/blob/master/src/helpers/sign/bscSignerHelper.js#L21
        let bscFormattedPayload = {
          sender: payload.from,
          recipient: payload.to,
          amount: payload.value,
          gasLimit: payload.gasLimit,
          gasPrice: payload.gasPrice,
          nonce: payload.nonce,
          chainId: payload.chainId,
        };

        return bscFormattedPayload;
      } catch (err) {
        console.error(err);
      }
    }
  }

  async prepareKaspaTransaction(
    senderAddresses: AuthAddress[],
    receiverAddress: string,
    amount: number,
  ): Promise<any> {
    const amountInKas = amount * 10 ** 8;

    return await WalletBalanceInstance.post(`/prepare-kaspa-tx`, {
      senderAddresses: senderAddresses.map((address: AuthAddress) => address.address),
      receiverAddress: receiverAddress,
      amount: amountInKas,
    });
  }

  async sendPreparedTx(tx: any, privKey: string, wallet: AuthUserWallet): Promise<any> {
    try {
      let signedTx;
      if (wallet.blockchain === 'ethereum' || wallet.blockchain === 'avalanche') {
        const wallet = new Wallet(privKey);
        signedTx = await wallet.signTransaction(tx);
      } else if (wallet.blockchain === 'trx') {
        const tronWeb = new TronWeb({
          fullHost: 'https://api.trongrid.io',
          privateKey: privKey,
        });

        console.log('tx', tx);
        let txWithSignature = await tronWeb.trx.sign(tx.tx);
        signedTx = txWithSignature;
        console.log('signed', signedTx);
      } else if (wallet.blockchain === 'xrp') {
        console.log('wallet from mnemonic? ', wallet.mnemonic);
        const xrpWallet = xrplWallet.fromMnemonic(wallet.mnemonic);

        console.log('Wallet', wallet);

        console.log('xrpWallet', xrpWallet);
        const {tx_blob: signed_tx_blob} = await xrpWallet.sign(tx);

        signedTx = signed_tx_blob;
      } else if (wallet.blockchain === 'binance-smart-chain') {
        const signService = new Services.SignService(wallet.blockchain, wallet.network);
        signedTx = await signService.signPreparedTransactionLocally(privKey, tx);
      } else {
        const signService = new Services.SignService(wallet.blockchain, wallet.network);
        signedTx = await signService.signPreparedTransactionLocally(
          wallet.zPub.accountXpriv,
          tx.data,
        );
      }

      if (wallet.blockchain !== 'trx' && wallet.blockchain !== 'avalanche') {
        try {
          return await WalletBalanceInstance.post(`/broadcast/${wallet.blockchain}`, {
            txhex: signedTx.raw ? signedTx.raw : signedTx,
          });
        } catch (err) {
          console.error(err);
        }
      } else {
        return await WalletBalanceInstance.post(
          `/broadcast-transaction/${
            wallet.blockchain == 'trx' ? 'tron' : wallet.blockchain
          }`,
          {
            tx: signedTx,
          },
        );
      }
    } catch (err) {
      console.error(err);
    }
  }

  async updateLastLoggedIn(bitcoinAddress: string): Promise<any> {
    return await WalletLoggerInstance.post(`/v1/wallet/${bitcoinAddress}/last-logged-in`);
  }
}
